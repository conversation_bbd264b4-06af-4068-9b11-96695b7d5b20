<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>Hello页面 - 学生信息输入表单</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .nav { margin-bottom: 20px; padding: 10px; background-color: #f0f0f0; }
        .nav a { margin-right: 15px; text-decoration: none; color: #007bff; }
        .nav a:hover { text-decoration: underline; }
        .form-group { margin-bottom: 10px; }
        input, select { margin: 5px; }
        .current { font-weight: bold; color: #28a745; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div class="nav">
        <strong>导航：</strong>
        <a href="<%= request.getContextPath() %>/">首页</a>
        <span class="current">Hello页面</span>
        <a href="<%= request.getContextPath() %>/show">Show页面</a>
    </div>

    <h2>Hello页面 - 学生信息输入表单</h2>
    <p style="color: #007bff;">欢迎来到Hello页面！这里是学生信息输入表单。</p>

    <form method="post" action="<%= request.getContextPath() %>/show">
        <div class="form-group">
            姓名：<input type="text" name="name" required><br>
        </div>
        <div class="form-group">
            性别: <input type="radio" name="gender" value="男" checked>男 <input type="radio" name="gender" value="女">女<br>
        </div>
        <div class="form-group">
            省：<select name="province">
                <option value="北京">北京</option>
                <option value="上海">上海</option>
                <option value="广东">广东</option>
                <option value="浙江">浙江</option>
            </select><br>
        </div>
        <div class="form-group">
            熟悉的语言：<select name="langs" multiple size="4">
                <option value="java">java</option>
                <option value="js">js</option>
                <option value="html">html</option>
                <option value="python">python</option>
                <option value="sql">sql</option>
            </select><br>
        </div>
        <div class="form-group">
            密码：<input type="password" name="pass" required><br>
        </div>
        <div class="form-group">
            爱好：<input type="checkbox" name="hobbies" value="电影">电影
                  <input type="checkbox" name="hobbies" value="美食">美食
                  <input type="checkbox" name="hobbies" value="旅游">旅游<br>
        </div>
        <div class="form-group">
            年龄：<input type="number" name="age" required><br>
        </div>
        <div class="form-group">
            <input type="submit" value="提交到Show页面"> <input type="reset" value="重置">
        </div>
    </form>

    <hr>
    <p>专业班级: [填写你的专业班级]</p>
    <p>学号(后四位): [填写你的学号后四位]</p>
    <p>姓名: [填写你的姓名]</p>
</body>
</html>