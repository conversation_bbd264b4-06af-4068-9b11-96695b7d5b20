<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.Arrays" %>
<%@ page import="entity.Stu" %>
<html>
<head>
    <title>学生信息显示</title>
</head>
<body>
<%
    Stu stu = new Stu();
    stu.setName(request.getParameter("name"));
    stu.setPass(request.getParameter("pass"));
    stu.setGender(request.getParameter("gender"));
    stu.setProvince(request.getParameter("province"));
    stu.setLangs(request.getParameterValues("langs"));
    stu.setHobbies(request.getParameterValues("hobbies"));
    stu.setAge(Integer.parseInt(request.getParameter("age")));
%>
<h2>学生信息如下：</h2>
<p>姓名：<%= stu.getName() %></p>
<p>性别：<%= stu.getGender() %></p>
<p>省：<%= stu.getProvince() %></p>
<p>熟悉的语言：<%= Arrays.toString(stu.getLangs()) %></p>
<p>密码：<%= stu.getPass() %></p>
<p>爱好：<%= Arrays.toString(stu.getHobbies()) %></p>
<p>年龄：<%= stu.getAge() %></p>
<br>
专业班级: [填写你的专业班级]<br>
学号(后四位): [填写你的学号后四位]<br>
姓名: [填写你的姓名]
</body>
</html>