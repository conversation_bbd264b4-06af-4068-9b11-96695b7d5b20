package entity;

import annotations.TableName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@TableName("sys_order")
public class SysOrder implements Serializable {

    private Long id;

    private Long userId;

    private String orderCode;

    private String createTime;

    private String updateTime;

    private String createUser;

    private String createUserRealName;

    private String updateUser;

    private String updateUserRealName;

    private String remark;
}
