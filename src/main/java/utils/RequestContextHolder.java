package utils;

import javax.servlet.http.HttpServletRequest;

public class RequestContextHolder {

    private static final ThreadLocal<HttpServletRequest> requestContextThreadLocal = new ThreadLocal<>();

    public static void setRequestContext(HttpServletRequest request) {
        requestContextThreadLocal.set(request);
    }

    public static HttpServletRequest getRequestContext() {
        return requestContextThreadLocal.get();
    }

    public static void removeRequestContext() {
        requestContextThreadLocal.remove();
    }

}
