package utils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class MemoryCache {

    private static final Map<String, Object> cache = new ConcurrentHashMap<>();

    @SuppressWarnings("unchecked")
    public static <T> T get(String key) {
        return (T) cache.get(key);
    }

    public static void set(String key, Object value) {
        cache.put(key, value);
    }

    public static void remove(String key) {
        cache.remove(key);
    }
}
