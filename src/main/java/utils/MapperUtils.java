package utils;

import lombok.SneakyThrows;
import lombok.ToString;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@ToString
public class MapperUtils {

    public static final Map<Class<?>, Object> mapperCache = new ConcurrentHashMap<>();

    @SuppressWarnings("unchecked")
    @SneakyThrows
    public static <T> T get(Class<?> key) {
        return (T) mapperCache.get(key);
    }

    public static void set(Class<?> key, Object value) {
        mapperCache.put(key, value);
    }

    public static void remove(Class<?> key) {
        mapperCache.remove(key);
    }

    public static String show() {
        StringBuilder stringBuilder = new StringBuilder();
        mapperCache.forEach((k, v) -> stringBuilder.append("[").append(v.getClass().getSimpleName().split("\\$\\$")[0]).append("]").append(","));
        stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        return stringBuilder.toString();
    }
}
