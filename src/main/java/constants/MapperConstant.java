package constants;

public interface MapperConstant {

    String INSERT_PREFIX = "INSERT INTO ";

    String UPDATE_PREFIX_1 = "UPDATE ";
    String UPDATE_PREFIX_2 = "SET ";

    static String getUpdatePrefix(String tableName) {
        return UPDATE_PREFIX_1 + tableName + UPDATE_PREFIX_2;
    }

    String DELETE_PREFIX = "DELETE FROM ";

    String SELECT_PREFIX = "SELECT ";

    static String getSelectCountSql(String tableName) {
        return SELECT_PREFIX + "COUNT(*) FROM " + tableName;
    }

    static String getSelectPrefix(String tableName) {
        return SELECT_PREFIX + "* FROM " + tableName;
    }

    static String getSelectPrefix(String tableName, String columnName) {
        return SELECT_PREFIX + columnName + " FROM " + tableName;
    }

}
