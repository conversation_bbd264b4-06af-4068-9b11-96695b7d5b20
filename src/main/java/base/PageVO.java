package base;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@ToString
public class PageVO<T> implements Serializable {

    private Integer pageNum;

    private Integer pageSize;

    private Integer total;

    private Integer pages;

    private List<T> records;

    public PageVO() {
        this.pageNum = 0;
        this.pageSize = 0;
        this.total = 0;
        this.pages = 0;
        this.records = new ArrayList<>();
    }

}
