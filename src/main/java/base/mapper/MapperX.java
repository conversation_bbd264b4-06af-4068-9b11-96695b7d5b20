package base.mapper;

import annotations.TableName;
import cn.hutool.core.util.StrUtil;

import java.lang.reflect.Field;

public interface MapperX {
    static <T> String getTableName(Class<T> clazz) {
        if (clazz == null) {
            throw new IllegalArgumentException("调用getTableName方法，参数不能为空");
        }
        TableName tableName = clazz.getAnnotation(TableName.class);
        if (tableName != null) {
            return tableName.value();
        }
        return clazz.getSimpleName();
    }

    static String getFieldNames(Class<?> clazz) {
        Field[] declaredFields = clazz.getDeclaredFields();
        StringBuilder stringBuilder = new StringBuilder();
        for (Field declaredField : declaredFields) {
            stringBuilder.append(StrUtil.toCamelCase(declaredField.getName()));
            stringBuilder.append(",");
        }
        return stringBuilder.substring(0, stringBuilder.length() - 1);
    }

    static String getParams(int length) {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < length; i++) {
            stringBuilder.append("?");
            stringBuilder.append(",");
        }
        return stringBuilder.substring(0, stringBuilder.length() - 1);
    }
}
