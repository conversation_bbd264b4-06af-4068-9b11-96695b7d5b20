package controller.data;

import base.R;
import controller.data.base.BaseController;
import entity.SysUser;
import mapper.SysUserMapper;
import utils.MapperUtils;
import utils.ServletUtils;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@WebServlet(urlPatterns = "/user/*")
public class SysUserController extends BaseController {

    private final static SysUserMapper sysUserMapper = MapperUtils.get(SysUser.class);

    public SysUserController() {
        super.addDefaultMethodInfo(SysUserController.class);
//        super.addMethod("page", ReflectUtil.getMethod(SysUserController.class, "handlePage"));
    }

    @Override
    public void handlePage(HttpServletRequest request, HttpServletResponse response) {
        ServletUtils.PageDTO pageDTO = ServletUtils.getPageVO();
        R.success(sysUserMapper.page(pageDTO.getPageNum(), pageDTO.getPageSize())).push(response);
    }

    @Override
    public void handleSave(HttpServletRequest request, HttpServletResponse response) {
        SysUser queryObj = ServletUtils.getPageVO().getQueryObj(SysUser.class);
        sysUserMapper.save(queryObj);
        R.success().setMsg("保存成功").push(response);
    }

    @Override
    public void handleUpdate(HttpServletRequest request, HttpServletResponse response) {
        SysUser queryObj = ServletUtils.getPageVO().getQueryObj(SysUser.class);
        sysUserMapper.updateById(queryObj);
        R.success().push(response);
    }

    @Override
    public void handleRemove(HttpServletRequest request, HttpServletResponse response) {
        String id = request.getParameter("id");
        sysUserMapper.removeById(Long.valueOf(id));
        R.success().push(response);
    }

    public void test(HttpServletRequest request, HttpServletResponse response) {
        R.success("测试").push(response);
    }
}
