package controller.pages;

import base.R;
import lombok.SneakyThrows;
import utils.ExceptionUtil;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 主页面路由控制器
 * 处理根路径下的页面访问，如 /hello, /show 等
 */
@WebServlet(urlPatterns = {"/hello", "/show", "/index"})
public class MainPageController extends HttpServlet {

    @Override
    protected void service(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        try {
            req.setCharacterEncoding("UTF-8");
            resp.setCharacterEncoding("UTF-8");

            String requestURI = req.getRequestURI();
            String contextPath = req.getContextPath();
            
            // 移除上下文路径，获取纯路径
            String path = requestURI.replace(contextPath, "");
            if (path.startsWith("/")) {
                path = path.substring(1);
            }

            String targetPage = null;
            
            // 根据路径确定目标页面
            switch (path) {
                case "hello":
                    targetPage = "/pages/hello.jsp";
                    break;
                case "show":
                    targetPage = "/pages/show.jsp";
                    break;
                case "index":
                    targetPage = "/index.jsp";
                    break;
                default:
                    // 未知路径，跳转到404
                    resp.sendRedirect(req.getContextPath() + "/404.jsp");
                    return;
            }

            // 检查目标页面是否存在
            if (!pageExists(targetPage)) {
                resp.sendRedirect(req.getContextPath() + "/404.jsp");
                return;
            }

            // 转发到目标页面
            req.getRequestDispatcher(targetPage).forward(req, resp);
            
        } catch (Exception e) {
            e.printStackTrace();
            R.error().setMsg("页面访问异常: " + ExceptionUtil.getExceptionMessage(e)).push(resp);
        }
    }

    /**
     * 检查页面是否存在
     */
    @SneakyThrows
    private boolean pageExists(String pagePath) {
        try (var is = getServletContext().getResourceAsStream(pagePath)) {
            return is != null;
        } catch (Exception e) {
            return false;
        }
    }
}
