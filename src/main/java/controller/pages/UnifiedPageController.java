package controller.pages;

import base.R;
import lombok.SneakyThrows;
import utils.ExceptionUtil;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@WebServlet(urlPatterns = "/pages/*")
public class UnifiedPageController extends HttpServlet {

    /**
     * 提取 URI 中 /pages/ 后面的部分作为页面名
     * 例如：/pages/hello → hello；/pages/user/list → user/list
     */
    private String extractAction(String uri) {
        // 1. 去除上下文路径（若有）
        String contextPath = getServletContext().getContextPath();
        String path = uri.replace(contextPath, "");

        // 2. 截取 /pages/ 后面的部分（支持多级路径，如 /pages/user/list → user/list）
        if (path.startsWith("/pages/")) {
            return path.substring("/pages/".length());
        }
        // 若直接访问 /pages，返回默认页面（如 index）或空
        return "";
    }

    @Override
    protected void service(HttpServletRequest req, HttpServletResponse resp) {
        try {
            req.setCharacterEncoding("UTF-8");
            resp.setCharacterEncoding("UTF-8");

            String pages = extractAction(req.getRequestURI());
            // 处理直接访问 /pages 的情况（默认跳转到首页）
            if (pages.isEmpty()) {
                pages = "index"; // 假设默认页面是 /pages/index.jsp
            }
            String pagesPath = "/pages/" + pages + ".jsp";

            // 检查页面是否存在（放宽判断，避免误判）
            if (!pagesExists(pagesPath)) {
                resp.sendRedirect(req.getContextPath() + "/404.jsp"); // 确保 404 页面存在
                return;
            }

            // 转发到目标 JSP
            req.getRequestDispatcher(pagesPath).forward(req, resp);
        } catch (Exception e) {
            e.printStackTrace();
            R.error().setMsg("请求异常: " + ExceptionUtil.getExceptionMessage(e)).push(resp);
        }
    }

    /**
     * 检查页面是否存在（兼容不同环境的资源访问）
     */
    @SneakyThrows
    private boolean pagesExists(String pagesPath) {
        // 方式1：通过资源流判断（比 getResource 更可靠）
        try (var is = getServletContext().getResourceAsStream(pagesPath)) {
            return is != null;
        } catch (Exception e) {
            return false;
        }
    }
}